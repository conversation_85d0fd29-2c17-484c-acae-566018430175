client:
  customer-id: 12345    # 客户ID, 必填项
  client-id: 223        # 客户端ID，必填项

comms:
  addrs:                # 服务器地址列表，至少需包含一个服务器
    - "comm.com:12345"
    - "127.0.0.1:50051"
  tls:                  # TLS 加密配置
    enabled: false      # 是否启用 TLS 加密（默认：false，保持向后兼容）
    cert-dir: "./certs" # 证书文件目录（默认：./certs）
    cert-file: "server.crt"  # 证书文件名（默认：server.crt）
    key-file: "server.key"   # 私钥文件名（默认：server.key）
    skip-verify: true   # 跳过服务器证书验证（默认：true，适用于自签名证书）
    auto-generate: true # 自动生成自签名证书（默认：true）

logging:
  level: "DEBUG"        # 日志级别
  format: "json"        # 日志格式
  outputs:              # 日志输出配置
    - type: "file"
      file: "/var/log/agent.log"
      maxSize: 128
      maxAge: 30
      maxBackups: 10
      compress: true
    - type: "console"
      stderr: false
