# UniSASE Agent配置管理应用实施指南

## 概述

基于对CGI框架Web系统的深入分析，本指南提供了构建Agent配置管理应用的具体实施方案。该应用将采用现代化的API架构，结合现有系统的安全机制和最佳实践。

## 项目结构

### 推荐目录结构

```
package/agent_config/
├── app.inf                 # 应用元信息配置
├── app.png                 # 应用图标 (64x64)
├── appctrl                 # 应用控制脚本
├── config/                 # 配置文件目录
│   └── default.yaml        # 默认配置模板
└── web/                    # Web模块目录
    ├── webmain             # 主入口页面
    ├── ajax_config         # 配置管理API
    ├── ajax_status         # 状态监控API
    ├── ajax_backup         # 备份管理API
    ├── config_export       # 配置导出功能
    ├── config_import       # 配置导入功能
    └── html/               # 静态资源目录
        ├── main.html       # 主界面HTML
        ├── css/            # 样式文件
        └── js/             # JavaScript文件
```

## 核心文件实现

### 1. 应用配置文件 (app.inf)

```ini
app_type=sys
app_id=APPMSY000007
app_name="agent_config"
app_version="1.0.0"
app_width=1280
app_height=800
app_cname="Agent配置管理"
app_desc="UniSASE Agent配置管理界面，提供配置编辑、备份恢复、状态监控等功能"
app_cname_en="Agent Configuration Manager"
app_desc_en="UniSASE Agent Configuration Management Interface with editing, backup, and monitoring capabilities"
```

### 2. 应用控制脚本 (appctrl)

```bash
#!/bin/sh

RAMDISK=/usr/ramdisk
APP_NAME="agent_config"
APP_PATH="${RAMDISK}/app/${APP_NAME}"
WEB_PATH="${RAMDISK}/admin/cgi-bin/App/${APP_NAME}"
CONFIG_PATH="/usr/panabit/app/unisase_agent/config"

agent_config_start() {
    # 创建必要目录
    mkdir -p "${WEB_PATH}"
    mkdir -p "${CONFIG_PATH}"
    
    # 复制Web文件
    cp -rf "${APP_PATH}/web/"* "${WEB_PATH}/"
    
    # 设置执行权限
    chmod +x "${WEB_PATH}/"*
    
    # 创建默认配置（如果不存在）
    if [ ! -f "${CONFIG_PATH}/config.yaml" ] && [ -f "${APP_PATH}/config/default.yaml" ]; then
        cp "${APP_PATH}/config/default.yaml" "${CONFIG_PATH}/config.yaml"
    fi
    
    echo "Agent Config Manager started successfully"
}

agent_config_stop() {
    # 清理Web文件
    rm -rf "${WEB_PATH}"
    echo "Agent Config Manager stopped successfully"
}

agent_config_status() {
    if [ -d "${WEB_PATH}" ] && [ -x "${WEB_PATH}/webmain" ]; then
        echo "running"
    else
        echo "stopped"
    fi
}

agent_config_verify() {
    # 检查依赖
    if [ ! -d "${RAMDISK}/admin/cgi-bin" ]; then
        echo "no"
        return 0
    fi
    
    echo "yes"
    return 1
}

case "$1" in
    start) agent_config_start ;;
    stop) agent_config_stop ;;
    status) agent_config_status ;;
    verify) agent_config_verify ;;
    restart)
        agent_config_stop
        sleep 1
        agent_config_start
        ;;
    *) echo "Usage: $0 {start|stop|restart|status|verify}" ;;
esac
```

### 3. 主入口页面 (web/webmain)

```bash
#!/bin/sh

# 设置弹窗参数
export CGI_pop_title="Agent配置管理"
export CGI_pop_route="App/agent_config/main"

# 调用通用弹窗处理
cd /usr/ramdisk/admin/cgi-bin/common
sh popup
```

### 4. 配置管理API (web/ajax_config)

```bash
#!/bin/sh

# 立即输出HTTP头
printf "Content-Type: application/json\r\n"
printf "Cache-Control: no-cache\r\n"
printf "Access-Control-Allow-Origin: *\r\n"
printf "Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS\r\n"
printf "Access-Control-Allow-Headers: Content-Type\r\n"
printf "\r\n"

# 处理CORS预检请求
if [ "$REQUEST_METHOD" = "OPTIONS" ]; then
    exit 0
fi

# 环境配置
PGPATH="${PGPATH:-/usr/panabit}"
APP_NAME="unisase_agent"
CONFIG_FILE="${PGPATH}/app/${APP_NAME}/config/config.yaml"
BACKUP_DIR="${PGPATH}/app/${APP_NAME}/backup"

# 错误处理函数
error_response() {
    echo "{\"error\": \"$1\", \"code\": ${2:-500}, \"timestamp\": \"$(date -Iseconds)\"}"
    exit 1
}

# 成功响应函数
success_response() {
    echo "{\"success\": true, \"message\": \"$1\", \"timestamp\": \"$(date -Iseconds)\"}"
}

# YAML转JSON函数
yaml_to_json() {
    local yaml_file="$1"
    
    if [ ! -f "$yaml_file" ]; then
        echo "{}"
        return
    fi
    
    # 简化的YAML解析（适用于基本结构）
    echo "{"
    local first=true
    local in_section=""
    local indent_level=0
    
    while IFS= read -r line; do
        # 跳过注释和空行
        [[ "$line" =~ ^[[:space:]]*# ]] && continue
        [[ "$line" =~ ^[[:space:]]*$ ]] && continue
        
        # 检测缩进级别
        local current_indent=$(echo "$line" | sed 's/[^ ].*//' | wc -c)
        current_indent=$((current_indent - 1))
        
        # 处理键值对
        if [[ "$line" =~ ^[[:space:]]*([^:]+):[[:space:]]*(.*)$ ]]; then
            local key="${BASH_REMATCH[1]// /}"
            local value="${BASH_REMATCH[2]}"
            
            # 移除引号
            value=$(echo "$value" | sed 's/^["'\'']//' | sed 's/["'\'']$//')
            
            if [ "$first" = false ]; then
                echo ","
            fi
            first=false
            
            if [ "$current_indent" -eq 0 ]; then
                in_section="$key"
                echo "  \"$key\": {"
            else
                echo "    \"$key\": \"$value\""
            fi
        fi
    done < "$yaml_file"
    
    echo "  }"
    echo "}"
}

# JSON转YAML函数
json_to_yaml() {
    local json_data="$1"
    local output_file="$2"
    
    # 提取主要配置项
    local customer_id=$(echo "$json_data" | grep -o '"customer-id":[[:space:]]*[0-9]*' | sed 's/"customer-id":[[:space:]]*//')
    local client_id=$(echo "$json_data" | grep -o '"client-id":[[:space:]]*[0-9]*' | sed 's/"client-id":[[:space:]]*//')
    local log_level=$(echo "$json_data" | grep -o '"level":[[:space:]]*"[^"]*"' | sed 's/"level":[[:space:]]*"//' | sed 's/"$//')
    local log_format=$(echo "$json_data" | grep -o '"format":[[:space:]]*"[^"]*"' | sed 's/"format":[[:space:]]*"//' | sed 's/"$//')
    
    # 设置默认值
    customer_id=${customer_id:-12345}
    client_id=${client_id:-223}
    log_level=${log_level:-INFO}
    log_format=${log_format:-json}
    
    # 生成YAML
    cat > "$output_file" << EOF
client:
  customer-id: $customer_id
  client-id: $client_id

comms:
  addrs:
    - "comm.example.com:12345"
    - "127.0.0.1:50051"
  tls:
    enabled: false
    cert-dir: "./certs"
    cert-file: "server.crt"
    key-file: "server.key"
    skip-verify: true
    auto-generate: true

logging:
  level: "$log_level"
  format: "$log_format"
  outputs:
    - type: "file"
      file: "/var/log/agent.log"
      maxSize: 128
      maxAge: 30
      maxBackups: 10
      compress: true
    - type: "console"
      stderr: false
EOF
}

# 处理GET请求 - 读取配置
handle_get() {
    yaml_to_json "$CONFIG_FILE"
}

# 处理POST请求 - 保存配置
handle_post() {
    local content_length="${CONTENT_LENGTH:-0}"
    if [ "$content_length" -eq 0 ]; then
        error_response "没有接收到数据" 400
    fi
    
    # 读取POST数据
    local post_data
    post_data=$(head -c "$content_length")
    
    # 验证JSON格式
    if ! echo "$post_data" | grep -q "^{.*}$"; then
        error_response "无效的JSON格式" 400
    fi
    
    # 创建配置目录
    local config_dir=$(dirname "$CONFIG_FILE")
    mkdir -p "$config_dir"
    mkdir -p "$BACKUP_DIR"
    
    # 备份现有配置
    if [ -f "$CONFIG_FILE" ]; then
        cp "$CONFIG_FILE" "$BACKUP_DIR/config.yaml.backup.$(date +%Y%m%d_%H%M%S)"
    fi
    
    # 生成新配置
    json_to_yaml "$post_data" "$CONFIG_FILE.tmp"
    
    # 验证配置文件
    if ! grep -q "^client:" "$CONFIG_FILE.tmp" || ! grep -q "^comms:" "$CONFIG_FILE.tmp"; then
        rm -f "$CONFIG_FILE.tmp"
        error_response "配置文件格式验证失败" 400
    fi
    
    # 原子性更新
    mv "$CONFIG_FILE.tmp" "$CONFIG_FILE"
    chmod 644 "$CONFIG_FILE"
    
    success_response "配置保存成功"
}

# 处理PUT请求 - 更新部分配置
handle_put() {
    error_response "PUT方法暂未实现" 501
}

# 处理DELETE请求 - 重置配置
handle_delete() {
    if [ -f "$CONFIG_FILE" ]; then
        # 备份当前配置
        cp "$CONFIG_FILE" "$BACKUP_DIR/config.yaml.deleted.$(date +%Y%m%d_%H%M%S)"
        rm -f "$CONFIG_FILE"
    fi
    
    success_response "配置已重置"
}

# 主处理逻辑
case "$REQUEST_METHOD" in
    "GET") handle_get ;;
    "POST") handle_post ;;
    "PUT") handle_put ;;
    "DELETE") handle_delete ;;
    *) error_response "不支持的请求方法: $REQUEST_METHOD" 405 ;;
esac
```

### 5. 状态监控API (web/ajax_status)

```bash
#!/bin/sh

printf "Content-Type: application/json\r\n"
printf "Cache-Control: no-cache\r\n"
printf "\r\n"

# 环境配置
PGPATH="${PGPATH:-/usr/panabit}"
APP_NAME="unisase_agent"
CONFIG_FILE="${PGPATH}/app/${APP_NAME}/config/config.yaml"
LOG_FILE="/var/log/agent.log"
BACKUP_DIR="${PGPATH}/app/${APP_NAME}/backup"

# 检查配置文件状态
check_config_status() {
    if [ -f "$CONFIG_FILE" ]; then
        echo "正常"
    else
        echo "缺失"
    fi
}

# 获取最后修改时间
get_last_modified() {
    if [ -f "$CONFIG_FILE" ]; then
        date -r "$CONFIG_FILE" "+%Y-%m-%d %H:%M:%S"
    else
        echo "未知"
    fi
}

# 检查服务连接状态
check_server_connectivity() {
    # 简单的连接测试
    if command -v nc >/dev/null 2>&1; then
        if nc -z 127.0.0.1 50051 2>/dev/null; then
            echo "true"
        else
            echo "false"
        fi
    else
        echo "false"
    fi
}

# 获取日志文件大小
get_log_file_size() {
    if [ -f "$LOG_FILE" ]; then
        local size=$(stat -c%s "$LOG_FILE" 2>/dev/null || echo "0")
        if [ "$size" -gt 1048576 ]; then
            echo "$(($size / 1048576)) MB"
        elif [ "$size" -gt 1024 ]; then
            echo "$(($size / 1024)) KB"
        else
            echo "$size B"
        fi
    else
        echo "0 B"
    fi
}

# 获取备份文件数量
get_backup_count() {
    if [ -d "$BACKUP_DIR" ]; then
        find "$BACKUP_DIR" -name "*.backup.*" | wc -l
    else
        echo "0"
    fi
}

# 获取系统负载
get_system_load() {
    if [ -f /proc/loadavg ]; then
        awk '{print $1}' /proc/loadavg
    else
        echo "0.00"
    fi
}

# 获取内存使用情况
get_memory_usage() {
    if [ -f /proc/meminfo ]; then
        local total=$(grep MemTotal /proc/meminfo | awk '{print $2}')
        local available=$(grep MemAvailable /proc/meminfo | awk '{print $2}')
        local used=$((total - available))
        local percentage=$((used * 100 / total))
        echo "$percentage"
    else
        echo "0"
    fi
}

# 生成状态报告
cat << EOF
{
  "config_status": "$(check_config_status)",
  "last_modified": "$(get_last_modified)",
  "server_connectivity": $(check_server_connectivity),
  "log_file_size": "$(get_log_file_size)",
  "backup_count": $(get_backup_count),
  "system_load": "$(get_system_load)",
  "memory_usage": "$(get_memory_usage)%",
  "timestamp": "$(date -Iseconds)"
}
EOF
```

## 前端实现

### 主界面HTML (web/html/main.html)

```html
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Agent配置管理</title>
    <link rel="stylesheet" href="../../../html/assert/css/layui.css">
    <link rel="stylesheet" href="../../../html/assert/panabit.css">
    <style>
        .config-container { padding: 20px; }
        .status-panel { margin-bottom: 20px; padding: 15px; background: #f8f8f8; border-radius: 5px; }
        .config-form { background: white; padding: 20px; border-radius: 5px; }
        .form-section { margin-bottom: 25px; }
        .form-section h3 { color: #333; border-bottom: 2px solid #1E9FFF; padding-bottom: 5px; }
        .status-item { display: inline-block; margin-right: 20px; }
        .status-good { color: #5FB878; }
        .status-warning { color: #FFB800; }
        .status-error { color: #FF5722; }
    </style>
</head>
<body>
    <div class="config-container">
        <!-- 状态面板 -->
        <div class="status-panel">
            <h3>系统状态</h3>
            <div class="status-item">
                <strong>配置状态:</strong> <span id="configStatus">检查中...</span>
            </div>
            <div class="status-item">
                <strong>最后修改:</strong> <span id="lastModified">-</span>
            </div>
            <div class="status-item">
                <strong>服务连接:</strong> <span id="serverStatus">检查中...</span>
            </div>
            <div class="status-item">
                <strong>日志大小:</strong> <span id="logSize">-</span>
            </div>
            <div class="status-item">
                <strong>备份数量:</strong> <span id="backupCount">-</span>
            </div>
        </div>

        <!-- 配置表单 -->
        <form class="layui-form config-form" lay-filter="configForm">
            <!-- 客户端配置 -->
            <div class="form-section">
                <h3>客户端配置</h3>
                <div class="layui-form-item">
                    <label class="layui-form-label">客户ID</label>
                    <div class="layui-input-block">
                        <input type="number" name="customer-id" placeholder="请输入客户ID" class="layui-input">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">客户端ID</label>
                    <div class="layui-input-block">
                        <input type="number" name="client-id" placeholder="请输入客户端ID" class="layui-input">
                    </div>
                </div>
            </div>

            <!-- 通信配置 -->
            <div class="form-section">
                <h3>通信配置</h3>
                <div class="layui-form-item">
                    <label class="layui-form-label">服务器地址</label>
                    <div class="layui-input-block">
                        <textarea name="addrs" placeholder="每行一个地址，格式：host:port" class="layui-textarea"></textarea>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">启用TLS</label>
                    <div class="layui-input-block">
                        <input type="checkbox" name="tls-enabled" lay-skin="switch" lay-text="开启|关闭">
                    </div>
                </div>
            </div>

            <!-- 日志配置 -->
            <div class="form-section">
                <h3>日志配置</h3>
                <div class="layui-form-item">
                    <label class="layui-form-label">日志级别</label>
                    <div class="layui-input-block">
                        <select name="log-level">
                            <option value="DEBUG">DEBUG</option>
                            <option value="INFO">INFO</option>
                            <option value="WARN">WARN</option>
                            <option value="ERROR">ERROR</option>
                        </select>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">日志格式</label>
                    <div class="layui-input-block">
                        <select name="log-format">
                            <option value="json">JSON</option>
                            <option value="text">文本</option>
                        </select>
                    </div>
                </div>
            </div>

            <!-- 操作按钮 -->
            <div class="layui-form-item">
                <div class="layui-input-block">
                    <button class="layui-btn" lay-submit lay-filter="saveConfig">保存配置</button>
                    <button type="button" class="layui-btn layui-btn-primary" id="reloadConfig">重新加载</button>
                    <button type="button" class="layui-btn layui-btn-normal" id="exportConfig">导出配置</button>
                    <button type="button" class="layui-btn layui-btn-warm" id="resetConfig">重置配置</button>
                </div>
            </div>
        </form>
    </div>

    <script src="../../../html/assert/layui.all.js"></script>
    <script src="js/config-manager.js"></script>
</body>
</html>
```

这个实施指南提供了完整的Agent配置管理应用架构和核心文件实现。接下来我将继续添加JavaScript实现和部署说明。
