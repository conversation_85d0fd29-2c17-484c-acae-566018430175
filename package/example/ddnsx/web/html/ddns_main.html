<link rel="stylesheet" href="../../../html/assert/css/layui.css">
<link rel="stylesheet" href="../../../html/assert/extent/paicon/iconfont.css">
<link rel="stylesheet" href="../../../html/assert/panabit.css">
<script type="text/javascript" src="../../../img/common.js"></script>
<script src="../../../html/assert/layui.all.js"></script>
<script src="../../../html/assert/panabit.js"></script>
<style>
    .paui-condtunnel-level-desc {
        padding: 1px 6px;
        width: 30px;
        color: #fff;
    }

    .rule-list {
        padding: 15px;
    }

    .rule-list ul li {
        overflow: hidden;
        margin-bottom: 10px;
    }

    .rule-list ul li>h5 {
        font-size: 13px;
        font-weight: bold;
        line-height: 22px;
        color: #333;
        width: 65px;
        float: left;
        text-align: justify;
    }

    .rule-list ul li>span {
        display: block;
        font-size: 13px;
        line-height: 22px;
        color: #444;
        width: calc(100% - 70px);
        float: right;
    }

    .paui-bg-critical {
        background-color: #730000;
    }

    /* body {
            padding: 10px;
        } */

    .layui-btn {
        line-height: 30px;
        padding: 0 6px;
        font-size: 12px;
    }

    .layui-table-box {
        height: calc(100% - 74px);
    }

    /*  .layui-table-column {
        position: absolute;
        width: calc(100% - 20px);
        min-height: 41px;
        padding: 8px 16px;
        border-width: 0;
        border-bottom-width: 1px;
        left: 10px;
        bottom: 25px;
    } */
</style>


<div class="main" style="height: calc(100vh - 20px);width: calc(100vw - 20px);background-color: white;padding: 10px;">

    <div class="layui-btn-container">
        <button class="layui-btn layui-btn-normal layui-btn-sm" id="submit_set" lay-on="submit_set">配置
            <i class="layui-icon layui-icon-set-fill" style="font-size: 16px;"></i>
        </button>
        <button type="button" class="layui-btn layui-btn-sm" id="submit_add" lay-on="submit_add">
            <i class="layui-icon" style="font-size: 16px;">&#xe67c;</i>添加
        </button>
    </div>

    <div class="paui-table-card"
        style="box-shadow: 0px 0px 0px 2px rgba(191, 191, 191, 0.3);height: calc(100% - 64px);">
        <table id="tab_set_log" lay-filter="tab_set_log"></table>
        <div class="layui-table-page" id="page_set_log"></div>
    </div>
</div>

<script>
    layui.use(function () {
        var $ = layui.$,
            layer = layui.layer,
            util = layui.util,
            ntmlog = layui.ntmlog,
            form = layui.form,
            laydate = layui.laydate,
            table = layui.table;
        upload = layui.upload;
        var element = layui.element;

        var api = '../../../cgi-bin/App/ddnsx/ajax_ddns_main';


        var event_center = {

            tabevt_delete: function (obj) {
                /*  console.log(obj); */
                let del_data = {};
                del_data.hostname = obj.data.hostname;
                del_data.model=obj.data.model;
                del_data.num=obj.index+1;
                del_data.action = "ddnsx_delete";
                $.ajax({
                    url: api,
                    async: false,
                    data: del_data,
                    success: function (res) {
                        /*  console.log(res); */
                        if (res.code == "0") {
                            table_render();
                            layer.msg('删除成功，已为您更新数据！');
                        } else {
                            layer.msg('删除失败，请稍后再试！');
                        }
                    },
                });
            },

            tabevt_log: function (obj) {
                popupWindow({
                    pop_title: '日志',                        // 必须参数，弹窗标题
                    pop_route: 'App/ddnsx/ddns_logger',
                    host: obj.data.hostname,   // 必须参数， 弹窗路径
                    model:obj.data.model,
                }, 1000, 500); // 弹窗宽度，  弹窗高度
                /*     var log_index = layer.open({
                        type: 1,
                        area: ['1000px', '500px'],
                        resize: false,
                        shadeClose: true,
                        title: '查看日志',
                        content: `<div class="paui-table-card" style="height: calc(100% - 10px);">
              <table id="tab_set_logs" lay-filter="tab_set_logs"></table>
             </div>`,
                        success: function () {
                            table_log(obj);
                        },
                    }); */
            },

            /*    tabevt_delete_log: function (obj) {
                   let log_data = {};
                   log_data.action = 'ddnsx_delete';
                   log_data.hostname = obj.data.hostname;
                   $.ajax({
                       url: '../../../cgi-bin/App/ddnsx/ajax_ddns_logger',
                       async: false,
                       data: log_data,
                       success: function (res) {
                           if (res.code == "0") {
                               table_log(obj);
                               layer.msg("删除成功，已为您更新数据！")
                           } else {
                               layer.msg('删除失败，请稍后再试！');
                           }
                       },
                   });
               }, */


            tabevt_edit: function (obj) {
                var set_index = layer.open({
                    type: 1,
                    area: ['800px', '650px'],
                    resize: false,
                    shadeClose: true,
                    title: '编辑DDNS',
                    content: ` <div class="layui-form" lay-filter="form_set" style="margin: 16px;">
                             <div class="ddns-set-container">
                                <div class="layui-form-item">
                                    <h5 style="color:red;">*DNS服务可用逗号,分割表示多个，如果不填，则会使用选线的DNS服务</h5>
                                    </div>
                                <div class="layui-form-item">
                                <label class="layui-form-label" style="width:120px;">域名：</label>
                                <div class="layui-input-inline">
                                <input type="text" name="hostname" value="" placeholder="" autocomplete="off" class="layui-input"  style="width:525px;">
                            </div>
                             </div>
                               <div class="layui-form-item" id="show_name" style="display:none;">
                                <label class="layui-form-label" style="width:120px;">主域名：</label>
                                <div class="layui-input-inline">
                                <input type="text" name="name" value="" placeholder="" autocomplete="off" class="layui-input"  style="width:525px;">
                               </div>
                             </div>
                             <div class="layui-form-item">
                                <label class="layui-form-label" style="width:120px;">绑定线路(主)：</label>
                                 <div class="layui-input-inline" style="width:250px;">
                                     <input type="text" name="linename_masters" lay-affix="search" lay-filter="search_linename_masters" lay-options="{split: true}" placeholder="请输入线路名称查询" class="layui-input">
                                    </div>
                                <div class="layui-input-inline" style="width:265px;">
                                    <select id="linename_master" name="linename_master">
                                        
                                    </select>
                               </div>
                             </div>
                              <div class="layui-form-item">
                                <label class="layui-form-label" style="width:120px;">绑定线路(备)：</label>
                                 <div class="layui-input-inline" style="width:250px;">
                                      <input type="text" name="linename_slaves" lay-affix="search" lay-filter="search_linename_slaves" lay-options="{split: true}" placeholder="请输入线路名称查询" autocomplete="off" class="layui-input">
                               </div>
                                <div class="layui-input-inline" style="width:265px;">
                                    <select id="linename_slave" name="linename_slave">

                                        </select>
                               </div>
                             </div>
                              <div class="layui-form-item">
                                <label class="layui-form-label" style="width:120px;">类型：</label>
                                <div class="layui-input-inline" style="width:525px;">
                                <select id="model" name="model" style="width:525px;">

                                    </select>
                               </div>
                             </div>
                              <div class="layui-form-item">
                                <label class="layui-form-label" style="width:120px;">DNS服务(主)：</label>
                                <div class="layui-input-inline">
                                <input type="text" name="DNSSvr_master" value="" placeholder="" autocomplete="off" class="layui-input">
                               </div>
                                <label class="layui-form-label" style="width:105px;">DNS服务(备)：</label>
                                <div class="layui-input-inline">
                                <input type="text" name="DNSSvr_slave" value="" placeholder="" autocomplete="off" class="layui-input">
                               </div>
                             </div>
                              <div class="layui-form-item">
                                <label class="layui-form-label" style="width:120px;">优先用IPv6地址：</label>
                                <div class="layui-input-inline" style="width:525px;">
                                    <select id="uip6" name="uip6" style="width:525px;">

                                        </select>
                               </div>
                             </div>
                              <div class="layui-form-item">
                                <label class="layui-form-label" style="width:120px;">域名供应商：</label>
                                <div class="layui-input-inline" style="width:460px;">
                                  <select id="type" name="type" lay-filter="type-select">

                                    </select>
                                    <a id="type_link" href="http://www.oray.com" style="color:#22a5f1;position:absolute;top:10px;left:460px;width:50px;margin-left:5px;">注册一下</a>
                               </div>
                             </div>
                             <div class="layui-form-item">
                                <label class="layui-form-label" style="width:120px;">用户名：</label>
                                <div class="layui-input-inline">
                                <input type="text" name="user" value="" placeholder="请输入不超过30个英文字符" autocomplete="off" class="layui-input" maxlength="128">
                               </div>
                                <label class="layui-form-label" style="width:110px;">密码：</label>
                                <div class="layui-input-inline">
                                <input type="password" lay-affix="eye" name="passwd" value="" placeholder="请输入不超过30个英文字符" autocomplete="off" class="layui-input" maxlength="128">
                               </div>
                             </div>
                              <div class="layui-form-item" id="show_zone_id" style="display:none;">
                                <label class="layui-form-label" style="width:120px;">域名ID：</label>
                                <div class="layui-input-inline">
                                <input type="password" lay-affix="eye" name="zone_id" value="" placeholder="示例：ff808082955cf4e601963385bfcb3e3c" autocomplete="off" class="layui-input" maxlength="128">
                               </div>
                             </div>
                             <div class="layui-form-item" id="show_recordset_id" style="position:absolute;left:350px;top:360px;display:none;">
                                <label class="layui-form-label" style="width:120px;">记录集ID：</label>
                                <div class="layui-input-inline">
                                <input type="password" lay-affix="eye" name="recordset_id" value="" placeholder="示例：ff808082955cfe120196338bf11034bf" autocomplete="off" class="layui-input" maxlength="128">
                               </div>
                             </div>
                             <div class="layui-form-item">
                                <label class="layui-form-label" style="width:120px;">使用状态：</label>
                                <div class="layui-input-inline" style="width:525px;">
                                <select id="disbled" name="disbled" style="width:525px;">

                                    </select>
                               </div>
                             </div>
                             <div class="layui-form-item">
                                <label class="layui-form-label" style="width:120px;">备注：</label>
                                <div class="layui-input-inline">
                                <input type="text" name="remark" value="" placeholder="请输入不超过200个字符" autocomplete="off" class="layui-input" maxlength="200" style="width:525px;">
                               </div>
                             </div>
                             <div class="layui-form-item">
                                 <button class="layui-btn layui-btn-normal" lay-submit lay-filter="form_sets" style="width:50px;position: absolute;bottom: -70px;right: 60px;">确定</button>
                                 <button class="layui-btn layui-btn-primary" lay-submit lay-filter="form_unsets" style="width:50px;position: absolute;bottom: -70px;right: 0px;">取消</button>
                                </div>
                             </div>
                        </div>`,
                    success: function () {
                        var uip6_list = [{ value: '0', text: '否' }, { value: '1', text: '是' }];
                        var disbled_list = [{ value: '0', text: '启用' }, { value: '1', text: '禁用' }];
                        var type_list = [{ value: '1', text: '花生壳(oray.com)' }, { value: '2', text: '公云(pubyun.com)' },
                        { value: '3', text: '88IP(88ip.cn)' }, { value: '4', text: 'noip(noip.com)' },
                        { value: '5', text: 'dyn(dyn.com)' }, { value: '6', text: 'changeip(changeip.com)' },
                        { value: '7', text: '阿里云(aliyun.com)' }, { value: '8', text: '腾讯云(tencent.com)' },
                        { value: '9', text: 'cloudflare(cloudflare.com)' }, { value: '0', text: 'huaweicloud(huaweicloud.com)' }];
                        var linename_master_list = [{ "id": '', "name": "自动", "type": "" }];
                        var linename_slave_list = [{ "id": '', "name": "自动", "type": "" }];
                        var model_list=[{ value: 'A', text: 'A记录(ipv4)' }, { value: 'AAAA', text: 'AAAA记录(ipv6)' }];
                        var uip6_html = document.getElementById('uip6');
                        var disbled_html = document.getElementById('disbled');
                        var type_html = document.getElementById('type');
                        var linename_master_html = document.getElementById('linename_master');
                        var linename_slave_html = document.getElementById('linename_slave');
                        var model_html=document.getElementById('model');
                        uip6_html.innerHTML = "";
                        disbled_html.innerHTML = "";
                        type_html.innerHTML = "";
                        linename_master_html.innerHTML = "";
                        linename_slave_html.innerHTML = "";
                        model_html.innerHTML='';
                        $.ajax({
                            url: '../../../cgi-bin/App/ddnsx/ajax_ddns_edit',
                            async: false,
                            data: { action: 'ddnsx_config_list', 'hostname': obj.data.hostname,'model':obj.data.model },
                            success: function (res) {
                                /*   console.log(res); */
                                form.val('form_set', res.data.data[0]);
                                type_list = type_list.filter(item => item.value == res.data.data[0].type).concat(type_list.filter(item => item.value != res.data.data[0].type));
                                disbled_list = disbled_list.filter(item => item.text == res.data.data[0].disbled).concat(disbled_list.filter(item => item.text != res.data.data[0].disbled));
                                uip6_list = uip6_list.filter(item => item.value == res.data.data[0].uip6).concat(uip6_list.filter(item => item.value != res.data.data[0].uip6));
                                model_list = model_list.filter(item => item.value == res.data.data[0].model).concat(model_list.filter(item => item.value != res.data.data[0].model));
                                type_list.forEach(e => {
                                    var opt = document.createElement('option');
                                    opt.value = e.value;
                                    opt.innerHTML = e.text;
                                    type_html.appendChild(opt);
                                });
                                if (type_list[0].value == '0') {
                                    $('#show_zone_id').css('display', 'block');
                                    $('#show_recordset_id').css('display', 'block');
                                    $('#show_name').css('display', 'none');
                                } else if(type_list[0].value == '8'||type_list[0].value == '7') {
                                    $('#show_zone_id').css('display', 'none');
                                    $('#show_recordset_id').css('display', 'none');
                                    $('#show_name').css('display', 'block');
                                }else{
                                     $('#show_zone_id').css('display', 'none');
                                    $('#show_recordset_id').css('display', 'none');
                                    $('#show_name').css('display', 'none');
                                }
                                disbled_list.forEach(e => {
                                    var opt = document.createElement('option');
                                    opt.value = e.value;
                                    opt.innerHTML = e.text;
                                    disbled_html.appendChild(opt);
                                });
                                uip6_list.forEach(e => {
                                    var opt = document.createElement('option');
                                    opt.value = e.value;
                                    opt.innerHTML = e.text;
                                    uip6_html.appendChild(opt);
                                });
                                model_list.forEach(e => {
                                    var opt = document.createElement('option');
                                    opt.value = e.value;
                                    opt.innerHTML = e.text;
                                    model_html.appendChild(opt);
                                });
                                $.ajax({
                                    url: '../../../cgi-bin/App/ddnsx/ajax_ddns_edit',
                                    async: false,
                                    data: { action: 'ddnsx_line' },
                                    success: function (json) {
                                        /*  console.log(res); */
                                        json.data.data[0].forEach(e => {
                                            linename_master_list.push(e);
                                            linename_slave_list.push(e);
                                        });
                                        linename_master_list = linename_master_list.filter(item => item.name == res.data.data[0].linename_master).concat(linename_master_list.filter(item => item.name != res.data.data[0].linename_master));
                                        linename_slave_list = linename_slave_list.filter(item => item.name == res.data.data[0].linename_slave).concat(linename_slave_list.filter(item => item.name != res.data.data[0].linename_slave));
                                        linename_master_list.forEach(e => {
                                            var opt = document.createElement('option');
                                            opt.value = e.name;
                                            opt.innerHTML = e.name;
                                            linename_master_html.appendChild(opt);
                                        });
                                        linename_slave_list.forEach(e => {
                                            var opt = document.createElement('option');
                                            opt.value = e.name;
                                            opt.innerHTML = e.name;
                                            linename_slave_html.appendChild(opt);
                                        });
                                        form.render();
                                    },
                                });
                                form.on('input-affix(search_linename_masters)',function(data){
                               var value=data.elem.value; 
                               linename_master_list=(linename_master_list.filter(item=>item.name.indexOf(value)!==-1)).concat(linename_master_list.filter(item=>item.name.indexOf(value)===-1));
                               linename_master_html.innerHTML = "";
                               linename_master_list.forEach(e => {
                                    var opt = document.createElement('option');
                                    opt.value = e.name;
                                    opt.innerHTML = e.name;
                                    linename_master_html.appendChild(opt);
                                });
                                form.render('select');
                         });
                         form.on('input-affix(search_linename_slaves)',function(data){
                               var value=data.elem.value; 
                               linename_slave_list=(linename_slave_list.filter(item=>item.name.indexOf(value)!==-1)).concat(linename_slave_list.filter(item=>item.name.indexOf(value)===-1));
                               linename_slave_html.innerHTML = "";
                               linename_slave_list.forEach(e => {
                                    var opt = document.createElement('option');
                                    opt.value = e.name;
                                    opt.innerHTML = e.name;
                                    linename_slave_html.appendChild(opt);
                                });
                                form.render('select');
                         });
                                form.on('select(type-select)', function (data) {
                                    var link = document.getElementById('type_link');
                                    switch (data.value) {
                                        case '1':
                                            link.href = "http://www.oray.com";
                                            $('#show_zone_id').css('display', 'none');
                                            $('#show_recordset_id').css('display', 'none');
                                            $('#show_name').css('display', 'none');
                                            break;
                                        case '2':
                                            link.href = "http://www.pubyun.com";
                                            $('#show_zone_id').css('display', 'none');
                                            $('#show_recordset_id').css('display', 'none');
                                            $('#show_name').css('display', 'none');
                                            break;
                                        case '3':
                                            link.href = "http://www.88ip.cn";
                                            $('#show_zone_id').css('display', 'none');
                                            $('#show_recordset_id').css('display', 'none');
                                            $('#show_name').css('display', 'none');
                                            break;
                                        case '4':
                                            link.href = "http://www.noip.com";
                                            $('#show_zone_id').css('display', 'none');
                                            $('#show_recordset_id').css('display', 'none');
                                            $('#show_name').css('display', 'none');
                                            break;
                                        case '5':
                                            link.href = "http://www.dyn.com";
                                            $('#show_zone_id').css('display', 'none');
                                            $('#show_recordset_id').css('display', 'none');
                                            $('#show_name').css('display', 'none');
                                            break;
                                        case '6':
                                            link.href = "http://www.changeip.com";
                                            $('#show_zone_id').css('display', 'none');
                                            $('#show_recordset_id').css('display', 'none');
                                            $('#show_name').css('display', 'none');
                                            break;
                                        case '7':
                                            link.href = "https://wanwang.aliyun.com";
                                            $('#show_zone_id').css('display', 'none');
                                            $('#show_recordset_id').css('display', 'none');
                                            $('#show_name').css('display', 'block');
                                            break;
                                        case '8':
                                            link.href = "https://cloud.tencent.com";
                                            $('#show_zone_id').css('display', 'none');
                                            $('#show_recordset_id').css('display', 'none');
                                            $('#show_name').css('display', 'block');
                                            break;
                                        case '9':
                                            link.href = "https://dash.cloudflare.com";
                                            $('#show_zone_id').css('display', 'none');
                                            $('#show_recordset_id').css('display', 'none');
                                            $('#show_name').css('display', 'none');
                                            break;
                                        case '0':
                                            link.href = "https://auth.huaweicloud.com";
                                            $('#show_zone_id').css('display', 'block');
                                            $('#show_recordset_id').css('display', 'block');
                                            $('#show_name').css('display', 'none');
                                            break;
                                        default:
                                            link.href = "http://www.oray.com";
                                            $('#show_zone_id').css('display', 'none');
                                            $('#show_recordset_id').css('display', 'none');
                                            $('#show_name').css('display', 'none');
                                            break;
                                    };
                                });
                                form.render();
                            },
                        });


                        let check = 0;
                        form.on("submit(form_sets)", function (data) {
                            check++;
                            var field = data.field;
                            /*   console.log(field); */
                            if (check % 2 == 1) {
                                let set_data = {};
                                set_data = field;
                                if (set_data.linename_master == "自动") {
                                    set_data.linename_master = "auto";
                                    set_data.linename_master_id = -1;
                                } else {
                                    linename_master_list.forEach(e => {
                                        if (e.name == set_data.linename_master) {
                                            set_data.linename_master_id = e.id;
                                        }
                                    });
                                };
                                if (set_data.linename_slave == "自动") {
                                    set_data.linename_slave = "auto";
                                    set_data.linename_slave_id = -1;
                                } else {
                                    linename_slave_list.forEach(e => {
                                        if (e.name == set_data.linename_slave) {
                                            set_data.linename_slave_id = e.id;
                                        }
                                    });
                                };
                                set_data.num=obj.index+1;
                                set_data.action = "ddnsx_edit";

                                $.ajax({
                                    url: '../../../cgi-bin/App/ddnsx/ajax_ddns_edit',
                                    async: false,
                                    data: set_data,
                                    success: function (res) {
                                        if (res.code == "0") {
                                            layer.close(set_index);
                                            layer.msg("编辑成功，已为您更新数据！");
                                            table_render();
                                        } else {
                                            layer.msg("编辑失败，请稍后再试！");
                                        }

                                    },
                                });

                            }
                        });



                        form.on("submit(form_unsets)", function (data) {
                            layer.close(set_index);
                        });
                    },
                });
            },


        };

        var col_render = function (r) {
            /*   console.log(r); */
            switch (this.field) {

                case "hostname":
                    return '<span class="abtn" lay-event="tabevt_hostname">' + r.hostname + '</span>';

                case "model":
                    if(r.model=='A'){
                       return '<span class="abtn" lay-event="tabevt_model">' + 'A记录(ipv4)' + '</span>';
                    }else if(r.model=='AAAA'){
                        return '<span class="abtn" lay-event="tabevt_model">' + 'AAAA记录(ipv6)' + '</span>';
                    }else{
                        return '<span class="abtn" lay-event="tabevt_model">' + r.model + '</span>';
                    }

                case "stat":
                    if(r.stat=="已更新"){
                       return '<span class="nbtn" lay-event="tabevt_stat" style="color:green;">' + r.stat + '</span>'
                    }else{
                        return '<span class="nbtn" lay-event="tabevt_stat" style="color:red;">' + r.stat + '</span>'
                    }

                case "operate":
                    return '<span class="abtn" lay-event="tabevt_log">日志</span>&nbsp;&nbsp;<span class="abtn" lay-event="tabevt_edit">编辑</span>&nbsp;&nbsp;<span class="abtn" lay-event="tabevt_delete">删除</span>';

                case "type":
                    return '<span class="abtn" lay-event="tabevt_type">' + r.type + '</span>';

                case "delete_log":
                    return '<span class="abtn" lay-event="tabevt_delete_log">删除</span>';

            }
        };


        /* 页面渲染 */
        form.render();


        var parseList = function () {
            let select_cols = {
                cols: [[
                    { type: 'numbers', title: '序号' }
                    , { field: 'hostname', title: '域名', templet: col_render, align: 'left' }
                    , { field: 'linename_master', title: '绑定主线' }
                    , { field: 'linename_slave', title: '绑定副线' }
                    , { field: 'type', title: '域名供应商', templet: col_render }
                    , { field: 'user', title: '用户名' }
                    , { field: 'create_time', title: '创建时间' }
                    , { field: 'change_time', title: '修改时间' }
                    , { field: 'status', title: '状态' }
                    , { field: 'remark', title: '备注' }
                    , { field: 'model', title: '类型',templet:col_render }
                    , { field: 'stat', title: '更新状态',templet: col_render }
                    , { field: 'operate', title: '操作', width: 200, templet: col_render, align: 'center', }
                ]]
            };
            return select_cols.cols;

        };


        var parmas_set = { action: 'ddnsx_list' };

        var table_render = function () {
            table.render({
                elem: '#tab_set_log',
                height: 'full-{{ d.pop_route ? 144 : 110 }}',
                url: api,
                where: parmas_set,
                page: true,
                parseData: function (json) {

                    json.data.data.forEach(e => {
                        if (e.linename_master == 1 || e.linename_master == "1") {
                            e.linename_master = "自动";
                        }
                        if (e.linename_slave == 1 || e.linename_slave == "1") {
                            e.linename_slave = "自动";
                        }
                    });
                    json.count = json.data.total;
                    json.data = json.data.data;
                    /*   console.log(json); */

                    json.data = json.data.filter(function (element) {
                        return element !== undefined;
                    });


                    return json;
                },
                pageElem: 'page_set_log',
                cols: parseList(),


            });

        };

        table_render();


        var table_log = function (obj) {
            let log_data = {};
            log_data.action = 'ddnsx_log';
            log_data.hostname = obj.data.hostname;
            $.ajax({
                url: '../../../cgi-bin/App/ddnsx/ajax_ddns_logger',
                async: false,
                data: log_data,
                success: function (res) {
                    table.render({
                        elem: '#tab_set_logs',
                        page: true,
                        limits: [5, 10, 15],
                        limit: 5,
                        cols: [[
                            { type: 'numbers', title: '序号' }
                            , { field: 'hostname', title: '域名', templet: col_render, align: 'left' }
                            , { field: 'logger', title: '日志' }
                            , { field: 'delete_log', title: '操作', templet: col_render, align: 'center', }
                        ]],
                        data: res.data.data,
                    });
                },
            });
        };


        util.on('lay-on', {
            'submit_set': function () {
                var set_index = layer.open({
                    type: 1,
                    area: ['400px', '250px'],
                    resize: false,
                    shadeClose: true,
                    title: '配置DDNS参数',
                    content: ` <div class="layui-form" lay-filter="form_set" style="margin: 16px;">
                             <div class="ddns-set-container">
                                <div class="layui-form-item">
                                <label class="layui-form-label" style="width:120px;">DDNS更新间隔：</label>
                                <div class="layui-input-inline">
                                <input type="number" name="upinterval" value="0" min="0" placeholder="单位秒，默认留空300秒" autocomplete="off" class="layui-input">
                            </div>
                             </div>
                             <div class="layui-form-item">
                                <label class="layui-form-label" style="width:120px;">每条记录更新间隔：</label>
                                <div class="layui-input-inline">
                                <input type="number" name="subinterval" value="0" min="0" placeholder="单位秒，默认留空0秒" autocomplete="off" class="layui-input">
                            </div>
                             </div>
                             <div class="layui-form-item">
                                 <button class="layui-btn layui-btn-normal" lay-submit lay-filter="form_sets" style="width:50px;position: absolute;bottom: -90px;right: 60px;">确定</button>
                                 <button class="layui-btn layui-btn-primary" lay-submit lay-filter="form_unsets" style="width:50px;position: absolute;bottom: -90px;right: 0px;">取消</button>
                                </div>
                             </div>
                        </div>`,
                    success: function () {
                        $.ajax({
                            url: api,
                            async: false,
                            data: { action: 'ddnsx_config_list' },
                            success: function (res) {
                                /*   console.log(res); */
                                form.val('form_set', res.data);
                                form.render();
                            },
                        });
                        let check = 0;
                        form.on("submit(form_sets)", function (data) {
                            check++;
                            var field = data.field;
                            if (check % 2 == 1) {
                                let set_data = {};
                                set_data.action = "ddnsx_config";
                                if (field.upinterval == "") {
                                    set_data.upinterval = 300;
                                } else {
                                    set_data.upinterval = field.upinterval;
                                }

                                if (field.subinterval == "") {
                                    set_data.subinterval = 0;
                                } else {
                                    set_data.subinterval = field.subinterval;
                                }

                                $.ajax({
                                    url: api,
                                    async: false,
                                    data: set_data,
                                    success: function (res) {
                                        if (res.code == "0") {
                                            layer.close(set_index);
                                            layer.msg("配置成功，已为您更新数据！");
                                            table_render();
                                        } else {
                                            layer.msg("配置失败，请稍后再试！");
                                        }
                                    },
                                });

                            }
                        });

                        form.on("submit(form_unsets)", function (data) {
                            layer.close(set_index);
                        });
                    },
                });

                return false;
            },


            'submit_add': function () {
                var set_index = layer.open({
                    type: 1,
                    area: ['800px', '650px'],
                    resize: false,
                    shadeClose: true,
                    title: '添加DDNS',
                    content: ` <div class="layui-form" lay-filter="form_set" style="margin: 16px;">
                             <div class="ddns-set-container">
                                <div class="layui-form-item">
                                    <h5 style="color:red;">*DNS服务可用逗号,分割表示多个，如果不填，则会使用选线的DNS服务</h5>
                                    </div>
                                <div class="layui-form-item">
                                <label class="layui-form-label" style="width:120px;">域名：</label>
                                <div class="layui-input-inline">
                                <input type="text" name="hostname" value="" placeholder="" autocomplete="off" class="layui-input"  style="width:525px;">
                               </div>
                             </div>
                              <div class="layui-form-item" id="show_name" style="display:none;">
                                <label class="layui-form-label" style="width:120px;">主域名：</label>
                                <div class="layui-input-inline">
                                <input type="text" name="name" value="" placeholder="" autocomplete="off" class="layui-input"  style="width:525px;">
                            </div>
                             </div>
                             <div class="layui-form-item">
                                <label class="layui-form-label" style="width:120px;">绑定线路(主)：</label>
                                 <div class="layui-input-inline" style="width:250px;">
                                     <input type="text" name="linename_masters" lay-affix="search" lay-filter="search_linename_masters" lay-options="{split: true}" placeholder="请输入线路名称查询" class="layui-input">
                                    </div>
                                <div class="layui-input-inline" style="width:265px;">
                                    <select id="linename_master" name="linename_master">
                                        
                                    </select>
                               </div>
                             </div>
                              <div class="layui-form-item">
                                <label class="layui-form-label" style="width:120px;">绑定线路(备)：</label>
                                 <div class="layui-input-inline" style="width:250px;">
                                      <input type="text" name="linename_slaves" lay-affix="search" lay-filter="search_linename_slaves" lay-options="{split: true}" placeholder="请输入线路名称查询" autocomplete="off" class="layui-input">
                               </div>
                                <div class="layui-input-inline" style="width:265px;">
                                    <select id="linename_slave" name="linename_slave">

                                        </select>
                               </div>
                             </div>
                             <div class="layui-form-item">
                                <label class="layui-form-label" style="width:120px;">类型：</label>
                                <div class="layui-input-inline" style="width:525px;">
                                <select id="model" name="model" style="width:525px;">

                                    </select>
                               </div>
                             </div>
                              <div class="layui-form-item">
                                <label class="layui-form-label" style="width:120px;">DNS服务(主)：</label>
                                <div class="layui-input-inline">
                                <input type="text" name="DNSSvr_master" value="" placeholder="" autocomplete="off" class="layui-input">
                               </div>
                                <label class="layui-form-label" style="width:105px;">DNS服务(备)：</label>
                                <div class="layui-input-inline">
                                <input type="text" name="DNSSvr_slave" value="" placeholder="" autocomplete="off" class="layui-input">
                               </div>
                             </div>
                             <div class="layui-form-item">
                                <label class="layui-form-label" style="width:120px;">优先用IPv6地址：</label>
                                <div class="layui-input-inline" style="width:525px;">
                                    <select id="uip6" name="uip6" style="width:525px;">

                                        </select>
                               </div>
                             </div>
                              <div class="layui-form-item">
                                <label class="layui-form-label" style="width:120px;">域名供应商：</label>
                                <div class="layui-input-inline" style="width:460px;">
                                  <select id="type" name="type" lay-filter="type-select">

                                    </select>
                                    <a id="type_link" href="http://www.oray.com" style="color:#22a5f1;position:absolute;top:10px;left:460px;width:50px;margin-left:5px;">注册一下</a>
                               </div>
                             </div>
                             <div class="layui-form-item">
                                <label class="layui-form-label" style="width:120px;">用户名：</label>
                                <div class="layui-input-inline">
                                <input type="text" name="user" value="" placeholder="请输入不超过30个英文字符" autocomplete="off" class="layui-input" maxlength="128">
                               </div>
                                <label class="layui-form-label" style="width:101px;">密码：</label>
                                <div class="layui-input-inline">
                                <input type="password" lay-affix="eye" name="passwd" value="" placeholder="请输入不超过30个英文字符" autocomplete="off" class="layui-input" maxlength="128">
                               </div>
                             </div>
                             <div class="layui-form-item" id="show_zone_id" style="display:none;">
                                <label class="layui-form-label" style="width:120px;">域名ID：</label>
                                <div class="layui-input-inline">
                                <input type="password" lay-affix="eye" name="zone_id" value="" placeholder="示例：ff808082955cf4e601963385bfcb3e3c" autocomplete="off" class="layui-input" maxlength="128">
                               </div>
                             </div>
                             <div class="layui-form-item" id="show_recordset_id" style="position:absolute;left:350px;top:360px;display:none;">
                                <label class="layui-form-label" style="width:120px;">记录集ID：</label>
                                <div class="layui-input-inline">
                                <input type="password" lay-affix="eye" name="recordset_id" value="" placeholder="示例：ff808082955cfe120196338bf11034bf" autocomplete="off" class="layui-input" maxlength="128">
                               </div>
                             </div>
                             <div class="layui-form-item">
                                <label class="layui-form-label" style="width:120px;">使用状态：</label>
                                <div class="layui-input-inline" style="width:525px;">
                                <select id="disbled" name="disbled" style="width:525px;">

                                    </select>
                               </div>
                             </div>
                             <div class="layui-form-item">
                                <label class="layui-form-label" style="width:120px;">备注：</label>
                                <div class="layui-input-inline">
                                <input type="text" name="remark" value="" placeholder="请输入不超过200个字符" autocomplete="off" class="layui-input" maxlength="200" style="width:525px;">
                               </div>
                             </div>
                             <div class="layui-form-item">
                                 <button class="layui-btn layui-btn-normal" lay-submit lay-filter="form_sets" style="width:50px;position: absolute;bottom: -70px;right: 60px;">确定</button>
                                 <button class="layui-btn layui-btn-primary" lay-submit lay-filter="form_unsets" style="width:50px;position: absolute;bottom: -70px;right: 0px;">取消</button>
                                </div>
                             </div>
                        </div>`,
                    success: function () {
                        var uip6_list = [{ value: '0', text: '否' }, { value: '1', text: '是' }];
                        var disbled_list = [{ value: '0', text: '启用' }, { value: '1', text: '禁用' }];
                        var type_list = [{ value: '1', text: '花生壳(oray.com)' }, { value: '2', text: '公云(pubyun.com)' },
                        { value: '3', text: '88IP(88ip.cn)' }, { value: '4', text: 'noip(noip.com)' },
                        { value: '5', text: 'dyn(dyn.com)' }, { value: '6', text: 'changeip(changeip.com)' },
                        { value: '7', text: '阿里云(aliyun.com)' }, { value: '8', text: '腾讯云(tencent.com)' },
                        { value: '9', text: 'cloudflare(cloudflare.com)' }, { value: '0', text: 'huaweicloud(huaweicloud.com)' }];
                        var linename_master_list = [{ "id": '', "name": "自动", "type": "" }];
                        var linename_slave_list = [{ "id": '', "name": "自动", "type": "" }];
                        var model_list=[{ value: 'A', text: 'A记录(ipv4)' }, { value: 'AAAA', text: 'AAAA记录(ipv6)' }];
                        var uip6_html = document.getElementById('uip6');
                        var disbled_html = document.getElementById('disbled');
                        var type_html = document.getElementById('type');
                        var linename_master_html = document.getElementById('linename_master');
                        var linename_slave_html = document.getElementById('linename_slave');
                        var model_html=document.getElementById('model');
                        uip6_html.innerHTML = "";
                        disbled_html.innerHTML = "";
                        type_html.innerHTML = "";
                        linename_master_html.innerHTML = "";
                        linename_slave_html.innerHTML = "";
                        model_html.innerHTML='';
                        uip6_list.forEach(e => {
                            var opt = document.createElement('option');
                            opt.value = e.value;
                            opt.innerHTML = e.text;
                            uip6_html.appendChild(opt);
                        });
                        disbled_list.forEach(e => {
                            var opt = document.createElement('option');
                            opt.value = e.value;
                            opt.innerHTML = e.text;
                            disbled_html.appendChild(opt);
                        });
                        type_list.forEach(e => {
                            var opt = document.createElement('option');
                            opt.value = e.value;
                            opt.innerHTML = e.text;
                            type_html.appendChild(opt);
                        });
                        model_list.forEach(e => {
                            var opt = document.createElement('option');
                            opt.value = e.value;
                            opt.innerHTML = e.text;
                            model_html.appendChild(opt);
                        });
                        form.render();
                        $.ajax({
                            url: '../../../cgi-bin/App/ddnsx/ajax_ddns_add',
                            async: false,
                            data: { action: 'ddnsx_line' },
                            success: function (res) {
                                /*   console.log(res); */
                                res.data.data[0].forEach(e => {
                                    linename_master_list.push(e);
                                    linename_slave_list.push(e);
                                });
                                /*   linename_master_list = res.data.data[0];
                                  linename_slave_list = res.data.data[0]; */
                                linename_master_list.forEach(e => {
                                    var opt = document.createElement('option');
                                    opt.value = e.name;
                                    opt.innerHTML = e.name;
                                    linename_master_html.appendChild(opt);
                                });
                                linename_slave_list.forEach(e => {
                                    var opt = document.createElement('option');
                                    opt.value = e.name;
                                    opt.innerHTML = e.name;
                                    linename_slave_html.appendChild(opt);
                                });
                                form.render();
                            },
                        });
                         form.on('input-affix(search_linename_masters)',function(data){
                               var value=data.elem.value; 
                               linename_master_list=(linename_master_list.filter(item=>item.name.indexOf(value)!==-1)).concat(linename_master_list.filter(item=>item.name.indexOf(value)===-1));
                               linename_master_html.innerHTML = "";
                               linename_master_list.forEach(e => {
                                    var opt = document.createElement('option');
                                    opt.value = e.name;
                                    opt.innerHTML = e.name;
                                    linename_master_html.appendChild(opt);
                                });
                                form.render('select');
                         });
                         form.on('input-affix(search_linename_slaves)',function(data){
                               var value=data.elem.value; 
                               linename_slave_list=(linename_slave_list.filter(item=>item.name.indexOf(value)!==-1)).concat(linename_slave_list.filter(item=>item.name.indexOf(value)===-1));
                               linename_slave_html.innerHTML = "";
                               linename_slave_list.forEach(e => {
                                    var opt = document.createElement('option');
                                    opt.value = e.name;
                                    opt.innerHTML = e.name;
                                    linename_slave_html.appendChild(opt);
                                });
                                form.render('select');
                         });
                        form.on('select(type-select)', function (data) {
                            var link = document.getElementById('type_link');
                            switch (data.value) {
                                case '1':
                                    link.href = "http://www.oray.com";
                                    $('#show_zone_id').css('display', 'none');
                                    $('#show_recordset_id').css('display', 'none');
                                    $('#show_name').css('display', 'none');
                                    break;
                                case '2':
                                    link.href = "http://www.pubyun.com";
                                    $('#show_zone_id').css('display', 'none');
                                    $('#show_recordset_id').css('display', 'none');
                                    $('#show_name').css('display', 'none');
                                    break;
                                case '3':
                                    link.href = "http://www.88ip.cn";
                                    $('#show_zone_id').css('display', 'none');
                                    $('#show_recordset_id').css('display', 'none');
                                    $('#show_name').css('display', 'none');
                                    break;
                                case '4':
                                    link.href = "http://www.noip.com";
                                    $('#show_zone_id').css('display', 'none');
                                    $('#show_recordset_id').css('display', 'none');
                                    $('#show_name').css('display', 'none');
                                    break;
                                case '5':
                                    link.href = "http://www.dyn.com";
                                    $('#show_zone_id').css('display', 'none');
                                    $('#show_recordset_id').css('display', 'none');
                                    $('#show_name').css('display', 'none');
                                    break;
                                case '6':
                                    link.href = "http://www.changeip.com";
                                    $('#show_zone_id').css('display', 'none');
                                    $('#show_recordset_id').css('display', 'none');
                                    $('#show_name').css('display', 'none');
                                    break;
                                case '7':
                                    link.href = "https://wanwang.aliyun.com";
                                    $('#show_zone_id').css('display', 'none');
                                    $('#show_recordset_id').css('display', 'none');
                                    $('#show_name').css('display', 'block');
                                    break;
                                case '8':
                                    link.href = "https://cloud.tencent.com";
                                    $('#show_zone_id').css('display', 'none');
                                    $('#show_recordset_id').css('display', 'none');
                                    $('#show_name').css('display', 'block');
                                    break;
                                case '9':
                                    link.href = "https://dash.cloudflare.com";
                                    $('#show_zone_id').css('display', 'none');
                                    $('#show_recordset_id').css('display', 'none');
                                    $('#show_name').css('display', 'none');
                                    break;
                                case '0':
                                    link.href = "https://auth.huaweicloud.com";
                                    $('#show_zone_id').css('display', 'block');
                                    $('#show_recordset_id').css('display', 'block');
                                    $('#show_name').css('display', 'none');
                                    break;
                                default:
                                    link.href = "http://www.oray.com";
                                    $('#show_zone_id').css('display', 'none');
                                    $('#show_recordset_id').css('display', 'none');
                                    $('#show_name').css('display', 'none');
                                    break;
                            };
                        });

                        let check = 0;
                        form.on("submit(form_sets)", function (data) {
                            check++;
                            var field = data.field;
                            /*  console.log(field); */
                            if (check % 2 == 1) {
                                let set_data = {};
                                set_data = field;
                                if (set_data.linename_master == "自动") {
                                    set_data.linename_master = "auto";
                                    set_data.linename_master_id = -1;
                                } else {
                                    linename_master_list.forEach(e => {
                                        if (e.name == set_data.linename_master) {
                                            set_data.linename_master_id = e.id;
                                        }
                                    });
                                };
                                if (set_data.linename_slave == "自动") {
                                    set_data.linename_slave = "auto";
                                    set_data.linename_slave_id = -1;
                                } else {
                                    linename_slave_list.forEach(e => {
                                        if (e.name == set_data.linename_slave) {
                                            set_data.linename_slave_id = e.id;
                                        }
                                    });
                                };
                                set_data.action = "ddnsx_add";

                                $.ajax({
                                    url: '../../../cgi-bin/App/ddnsx/ajax_ddns_add',
                                    async: false,
                                    data: set_data,
                                    success: function (res) {
                                        if (res.code == "0") {
                                            layer.close(set_index);
                                            layer.msg("添加成功，已为您更新数据！");
                                            table_render();
                                        } else {
                                            layer.msg("添加失败，请稍后再试！");
                                        }

                                    },
                                });

                            }
                        });

                        form.on("submit(form_unsets)", function (data) {
                            layer.close(set_index);
                        });
                    }
                });
            },
        });


        table.on('tool(tab_set_log)', function (obj) {
            event_center[obj.event](obj);
        });

        /*   table.on('tool(tab_set_logs)', function (obj) {
              event_center[obj.event](obj);
          }); */
    });


</script>