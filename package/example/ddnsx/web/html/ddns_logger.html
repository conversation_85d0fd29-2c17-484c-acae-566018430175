<link rel="stylesheet" href="../../../html/assert/css/layui.css">
<link rel="stylesheet" href="../../../html/assert/extent/paicon/iconfont.css">
<link rel="stylesheet" href="../../../html/assert/panabit.css">
<script type="text/javascript" src="../../../img/common.js"></script>
<script src="../../../html/assert/layui.all.js"></script>
<script src="../../../html/assert/panabit.js"></script>
<style>
    .paui-condtunnel-level-desc {
        padding: 1px 6px;
        width: 30px;
        color: #fff;
    }

    .rule-list {
        padding: 15px;
    }

    .rule-list ul li {
        overflow: hidden;
        margin-bottom: 10px;
    }

    .rule-list ul li>h5 {
        font-size: 13px;
        font-weight: bold;
        line-height: 22px;
        color: #333;
        width: 65px;
        float: left;
        text-align: justify;
    }

    .rule-list ul li>span {
        display: block;
        font-size: 13px;
        line-height: 22px;
        color: #444;
        width: calc(100% - 70px);
        float: right;
    }

    .paui-bg-critical {
        background-color: #730000;
    }

    /* body {
        padding: 10px;
    } */

    .layui-btn {
        line-height: 30px;
        padding: 0 6px;
        font-size: 12px;
    }

    .layui-table-box {
        height: calc(100% - 74px);
    }

    /*  .layui-table-column {
    position: absolute;
    width: calc(100% - 20px);
    min-height: 41px;
    padding: 8px 16px;
    border-width: 0;
    border-bottom-width: 1px;
    left: 10px;
    bottom: 25px;
} */
</style>

<div class="main" style="height: 100vh;">
    <div class="paui-table-card" style="height: calc(100% - 10px);">
        <table id="tab_set_logs" lay-filter="tab_set_logs"></table>
    </div>
</div>

<script>
    layui.use(function () {
        var $ = layui.$,
            layer = layui.layer,
            util = layui.util,
            ntmlog = layui.ntmlog,
            form = layui.form,
            laydate = layui.laydate,
            table = layui.table;
        upload = layui.upload;
        var element = layui.element;

        var api = '../../../cgi-bin/App/ddnsx/ajax_ddns_main';

        var event_center = {

            tabevt_delete_log: function (obj) {
              /*   console.log(obj); */
                let log_data = {};
                log_data.action = 'ddnsx_delete';
                log_data.hostname = obj.data.hostname;
                log_data.model=model;
                $.ajax({
                    url: '../../../cgi-bin/App/ddnsx/ajax_ddns_logger',
                    async: false,
                    data: log_data,
                    success: function (res) {
                        /*  console.log(res); */
                        if (res.code == "0") {
                            table_log(obj);
                            layer.msg("删除成功，已为您更新数据！")
                        } else {
                            layer.msg('删除失败，请稍后再试！');
                        }
                    },
                });
            },

        };

        var col_render = function (r) {
            /*   console.log(r); */
            switch (this.field) {

                case "hostname":
                    return '<span class="abtn" lay-event="tabevt_hostname">' + r.hostname + '</span>';

                case "operate":
                    return '<span class="abtn" lay-event="tabevt_log">日志</span>&nbsp;&nbsp;<span class="abtn" lay-event="tabevt_edit">编辑</span>&nbsp;&nbsp;<span class="abtn" lay-event="tabevt_delete">删除</span>';

                case "type":
                    return '<span class="abtn" lay-event="tabevt_type">' + r.type + '</span>';

                case "delete_log":
                    return '<span class="abtn" lay-event="tabevt_delete_log">删除</span>';

            }
        };


        var model;
        var table_log = function () {
            let list = window.location.href.split("&");
            let hostname = list[3].split("=")[1];
            model=list[4].split("=")[1];
            let log_data = {};
            log_data.action = 'ddnsx_log';
            log_data.hostname = hostname;
            log_data.model=model;
            $.ajax({
                url: '../../../cgi-bin/App/ddnsx/ajax_ddns_logger',
                async: false,
                data: log_data,
                success: function (res) {
                    /*  console.log(res); */
                    table.render({
                        elem: '#tab_set_logs',
                        page: true,
                        limits: [5, 10, 15],
                        limit: 5,
                        /*   pageElem: 'page_set_logs', */
                        cols: [[
                            { type: 'numbers', title: '序号' }
                            , { field: 'hostname', title: '域名', templet: col_render, align: 'left' }
                            , { field: 'logger', title: '日志' }
                            , { field: 'delete_log', title: '操作', templet: col_render, align: 'center', }
                        ]],
                        data: res.data.data,
                    });
                },
            });
        };

        table_log();


        table.on('tool(tab_set_logs)', function (obj) {
            event_center[obj.event](obj);
        });
    });

</script>