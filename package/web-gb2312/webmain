#!/bin/sh
#This script is created by ssparser automatically. The parser first created by <PERSON><PERSON><PERSON><PERSON>
printf "Content-type: text/html;charset=gb2312
Cache-Control: no-cache

"
echo -n "";
if [ "${PALANG}" = "en" ]; then
	LANG_001="Client|Config"
	LANG_002="Communication"
	LANG_003="Logging"
	LANG_004="System|Status"
	LANG_005="Customer ID"
	LANG_006="Client ID"
	LANG_007="Server Address"
	LANG_008="TLS Settings"
	LANG_009="Enable TLS"
	LANG_010="Certificate Directory"
	LANG_011="Certificate File"
	LANG_012="Private Key File"
	LANG_013="Skip Verification"
	LANG_014="Auto Generate"
	LANG_015="Log Level"
	LANG_016="Log Format"
	LANG_017="Log File"
	LANG_018="Max Size (MB)"
	LANG_019="Max Age (Days)"
	LANG_020="Max Backups"
	LANG_021="Compress"
	LANG_022="Console Output"
	LANG_023="Load Config"
	LANG_024="Save Config"
	LANG_025="Reset"
	LANG_026="Add Address"
	LANG_027="Delete"
	LANG_028="Operation failed"
	LANG_029="Config saved successfully"
	LANG_030="Config loaded successfully"
fi
CGI_browsertitle="UniSASE Agent"
. ../../common/common.sh
cfgroot="/cgi-bin/App/unisase_agent"
myself="${cfgroot}/`basename $0`"
MOD_TAB_LIST="${LANG_001:=客户端配置}#${cfgroot}/webmain?tab=client ${LANG_002:=通信配置}#${cfgroot}/webmain?tab=comms ${LANG_003:=日志配置}#${cfgroot}/webmain?tab=logging ${LANG_004:=系统状态}#${cfgroot}/webmain?tab=status"

# 配置文件路径
CONFIG_FILE="${PGPATH}/app/unisase_agent/config/config.yaml"

echo -n "
<script type=\"text/javascript\" src=\"/img/common.js\"></script>
<script language=\"javascript\">
function validateForm() {
	var customerId = document.getElementById('customerId');
	var clientId = document.getElementById('clientId');
	
	if (customerId && customerId.value) {
		customerId.value = TrimAll(customerId.value);
		if (!IsDigit(customerId.value) || customerId.value <= 0) {
			alert('客户ID必须是正整数');
			customerId.select();
			return false;
		}
	}
	
	if (clientId && clientId.value) {
		clientId.value = TrimAll(clientId.value);
		if (!IsDigit(clientId.value) || clientId.value <= 0) {
			alert('客户端ID必须是正整数');
			clientId.select();
			return false;
		}
	}
	
	// 验证服务器地址
	var addrInputs = document.querySelectorAll('input[name=\"serverAddr\"]');
	for (var i = 0; i < addrInputs.length; i++) {
		var addr = TrimAll(addrInputs[i].value);
		if (addr && !addr.match(/^[a-zA-Z0-9.-]+:[0-9]+$/)) {
			alert('服务器地址格式错误，应为 host:port');
			addrInputs[i].select();
			return false;
		}
	}
	
	return true;
}

function addServerAddress() {
	var container = document.getElementById('serverAddrs');
	var div = document.createElement('div');
	div.className = 'addr-input';
	div.innerHTML = '<input type=\"text\" name=\"serverAddr\" placeholder=\"例如: comm.com:12345\" style=\"width:200px\"> <input type=\"button\" value=\"${LANG_019:=删除}\" onclick=\"removeServerAddress(this)\">';
	container.appendChild(div);
}

function removeServerAddress(btn) {
	var container = document.getElementById('serverAddrs');
	if (container.children.length > 1) {
		btn.parentElement.remove();
	} else {
		alert('至少需要保留一个服务器地址');
	}
}

// 全局变量保存原始配置数据
var originalConfig = null;

// 辅助函数：去除字符串首尾空白
function TrimAll(str) {
	return str ? str.replace(/^\s+|\s+$/g, '') : '';
}

// 辅助函数：获取原始配置中的值，如果不存在则返回默认值
function getOriginalValue(path, defaultValue) {
	if (!originalConfig) return defaultValue;

	var keys = path.split('.');
	var value = originalConfig;

	for (var i = 0; i < keys.length; i++) {
		if (value && typeof value === 'object' && keys[i] in value) {
			value = value[keys[i]];
		} else {
			return defaultValue;
		}
	}

	return value !== null && value !== undefined ? value : defaultValue;
}

function loadConfig() {
	ajxs({
		type: 'GET',
		url: '${cfgroot}/ajax_config_pure',
		error: function(status) {
			alert('加载配置失败: HTTP ' + status);
		},
		success: function(data) {
			try {
				// 清理数据中的多余空白字符
				data = data.replace(/^\s+|\s+$/g, '');

				// 检查数据是否为空
				if (!data || data.length === 0) {
					alert('服务器返回空数据');
					return;
				}

				// 检查是否为有效的JSON格式
				if (!data.startsWith('{') || !data.endsWith('}')) {
					alert('服务器返回的数据格式不正确: ' + data.substring(0, 100));
					return;
				}

				var config = eval('(' + data + ')');
				if (config.error) {
					alert('加载配置失败: ' + config.error);
					return;
				}

				// 保存原始配置数据，用于保存时的回退
				originalConfig = JSON.parse(JSON.stringify(config));

				populateForm(config);
				// 配置加载成功，不再显示调试消息
			} catch(e) {
				alert('解析配置数据失败: ' + e.message + '\\n数据内容: ' + data.substring(0, 200));
			}
		}
	});
}

function populateForm(config) {
	// 填充客户端配置
	if (config.client) {
		var customerId = document.getElementById('customerId');
		var clientId = document.getElementById('clientId');
		if (customerId) customerId.value = config.client['customer-id'] || '';
		if (clientId) clientId.value = config.client['client-id'] || '';
	}

	// 填充服务器地址
	if (config.comms && config.comms.addrs) {
		var container = document.getElementById('serverAddrs');
		if (container) {
			container.innerHTML = '';
			for (var i = 0; i < config.comms.addrs.length; i++) {
				var div = document.createElement('div');
				div.className = 'addr-input';
				div.innerHTML = '<input type=\"text\" name=\"serverAddr\" value=\"' + config.comms.addrs[i] + '\" style=\"width:200px\"> <input type=\"button\" value=\"${LANG_027:=删除}\" onclick=\"removeServerAddress(this)\">';
				container.appendChild(div);
			}
		}
	}

	// 填充TLS配置
	if (config.comms && config.comms.tls) {
		var tlsEnabled = document.getElementById('tlsEnabled');
		var tlsCertDir = document.getElementById('tlsCertDir');
		var tlsCertFile = document.getElementById('tlsCertFile');
		var tlsKeyFile = document.getElementById('tlsKeyFile');
		var tlsSkipVerify = document.getElementById('tlsSkipVerify');
		var tlsAutoGenerate = document.getElementById('tlsAutoGenerate');

		if (tlsEnabled) tlsEnabled.checked = config.comms.tls.enabled === true;
		if (tlsCertDir) tlsCertDir.value = config.comms.tls['cert-dir'] || './certs';
		if (tlsCertFile) tlsCertFile.value = config.comms.tls['cert-file'] || 'server.crt';
		if (tlsKeyFile) tlsKeyFile.value = config.comms.tls['key-file'] || 'server.key';
		if (tlsSkipVerify) tlsSkipVerify.checked = config.comms.tls['skip-verify'] !== false;
		if (tlsAutoGenerate) tlsAutoGenerate.checked = config.comms.tls['auto-generate'] !== false;
	}

	// 填充日志配置
	if (config.logging) {
		var logLevel = document.getElementById('logLevel');
		var logFormat = document.getElementById('logFormat');

		if (logLevel) logLevel.value = config.logging.level || 'DEBUG';
		if (logFormat) logFormat.value = config.logging.format || 'json';

		if (config.logging.outputs) {
			// 查找文件输出配置
			for (var i = 0; i < config.logging.outputs.length; i++) {
				var output = config.logging.outputs[i];
				if (output.type === 'file') {
					var logFile = document.getElementById('logFile');
					var maxSize = document.getElementById('maxSize');
					var maxAge = document.getElementById('maxAge');
					var maxBackups = document.getElementById('maxBackups');
					var compress = document.getElementById('compress');

					if (logFile) logFile.value = output.file || '/var/log/agent.log';
					if (maxSize) maxSize.value = output.maxSize || 128;
					if (maxAge) maxAge.value = output.maxAge || 30;
					if (maxBackups) maxBackups.value = output.maxBackups || 10;
					if (compress) compress.checked = output.compress !== false;
				} else if (output.type === 'console') {
					var consoleStderr = document.getElementById('consoleStderr');
					if (consoleStderr) consoleStderr.checked = output.stderr === true;
				}
			}
		}
	}
}

function validateForm() {
	// 验证客户端配置
	var customerId = document.getElementById('customerId');
	var clientId = document.getElementById('clientId');

	if (customerId && customerId.value) {
		if (isNaN(customerId.value) || parseInt(customerId.value) <= 0) {
			alert('客户ID必须是正整数');
			customerId.focus();
			return false;
		}
	}

	if (clientId && clientId.value) {
		if (isNaN(clientId.value) || parseInt(clientId.value) <= 0) {
			alert('客户端ID必须是正整数');
			clientId.focus();
			return false;
		}
	}

	// 验证服务器地址格式
	var addrInputs = document.querySelectorAll('input[name=\"serverAddr\"]');
	for (var i = 0; i < addrInputs.length; i++) {
		var addr = TrimAll(addrInputs[i].value);
		if (addr && !addr.match(/^[^:]+:[0-9]+$/)) {
			alert('服务器地址格式不正确，应为: 主机名:端口号\\n例如: comm.com:12345');
			addrInputs[i].focus();
			return false;
		}
	}

	// 验证日志文件路径
	var logFile = document.getElementById('logFile');
	if (logFile && logFile.value && !logFile.value.match(/^\/.*\.log$/)) {
		alert('日志文件路径应为绝对路径且以.log结尾\\n例如: /var/log/agent.log');
		logFile.focus();
		return false;
	}

	return true;
}

function saveConfig() {
	if (!validateForm()) {
		return false;
	}
	
	// 收集表单数据
	var config = {
		client: {},
		comms: { addrs: [], tls: {} },
		logging: { outputs: [] }
	};

	// 收集客户端配置 - 使用原始值作为回退
	var customerId = document.getElementById('customerId');
	var clientId = document.getElementById('clientId');
	config.client['customer-id'] = customerId && customerId.value ? parseInt(customerId.value) : getOriginalValue('client.customer-id', 12345);
	config.client['client-id'] = clientId && clientId.value ? parseInt(clientId.value) : getOriginalValue('client.client-id', 223);

	// 收集服务器地址 - 确保至少有默认地址
	var addrInputs = document.querySelectorAll('input[name=\"serverAddr\"]');
	for (var i = 0; i < addrInputs.length; i++) {
		var addr = TrimAll(addrInputs[i].value);
		if (addr) config.comms.addrs.push(addr);
	}
	// 如果没有地址，使用原始配置中的地址
	if (config.comms.addrs.length === 0) {
		var originalAddrs = getOriginalValue('comms.addrs', ['comm.com:12345', '127.0.0.1:50051']);
		config.comms.addrs = Array.isArray(originalAddrs) ? originalAddrs : ['comm.com:12345', '127.0.0.1:50051'];
	}

	// 收集TLS配置 - 使用原始值作为回退
	var tlsEnabled = document.getElementById('tlsEnabled');
	var tlsCertDir = document.getElementById('tlsCertDir');
	var tlsCertFile = document.getElementById('tlsCertFile');
	var tlsKeyFile = document.getElementById('tlsKeyFile');
	var tlsSkipVerify = document.getElementById('tlsSkipVerify');
	var tlsAutoGenerate = document.getElementById('tlsAutoGenerate');

	config.comms.tls.enabled = tlsEnabled ? tlsEnabled.checked : getOriginalValue('comms.tls.enabled', false);
	config.comms.tls['cert-dir'] = tlsCertDir && tlsCertDir.value ? tlsCertDir.value : getOriginalValue('comms.tls.cert-dir', './certs');
	config.comms.tls['cert-file'] = tlsCertFile && tlsCertFile.value ? tlsCertFile.value : getOriginalValue('comms.tls.cert-file', 'server.crt');
	config.comms.tls['key-file'] = tlsKeyFile && tlsKeyFile.value ? tlsKeyFile.value : getOriginalValue('comms.tls.key-file', 'server.key');
	config.comms.tls['skip-verify'] = tlsSkipVerify ? tlsSkipVerify.checked : getOriginalValue('comms.tls.skip-verify', true);
	config.comms.tls['auto-generate'] = tlsAutoGenerate ? tlsAutoGenerate.checked : getOriginalValue('comms.tls.auto-generate', true);

	// 收集日志配置 - 使用原始值作为回退
	var logLevel = document.getElementById('logLevel');
	var logFormat = document.getElementById('logFormat');

	config.logging.level = logLevel && logLevel.value ? logLevel.value : getOriginalValue('logging.level', 'DEBUG');
	config.logging.format = logFormat && logFormat.value ? logFormat.value : getOriginalValue('logging.format', 'json');

	// 文件输出配置 - 使用原始值作为回退
	var logFile = document.getElementById('logFile');
	var maxSize = document.getElementById('maxSize');
	var maxAge = document.getElementById('maxAge');
	var maxBackups = document.getElementById('maxBackups');
	var compress = document.getElementById('compress');

	// 获取原始文件输出配置
	var originalFileOutput = null;
	if (originalConfig && originalConfig.logging && originalConfig.logging.outputs) {
		for (var i = 0; i < originalConfig.logging.outputs.length; i++) {
			if (originalConfig.logging.outputs[i].type === 'file') {
				originalFileOutput = originalConfig.logging.outputs[i];
				break;
			}
		}
	}

	var fileOutput = {
		type: 'file',
		file: logFile && logFile.value ? logFile.value : (originalFileOutput ? originalFileOutput.file : '/var/log/agent.log'),
		maxSize: maxSize && maxSize.value ? parseInt(maxSize.value) : (originalFileOutput ? originalFileOutput.maxSize : 128),
		maxAge: maxAge && maxAge.value ? parseInt(maxAge.value) : (originalFileOutput ? originalFileOutput.maxAge : 30),
		maxBackups: maxBackups && maxBackups.value ? parseInt(maxBackups.value) : (originalFileOutput ? originalFileOutput.maxBackups : 10),
		compress: compress !== null ? compress.checked : (originalFileOutput ? originalFileOutput.compress : true)
	};
	config.logging.outputs.push(fileOutput);

	// 控制台输出配置 - 使用原始值作为回退
	var consoleStderr = document.getElementById('consoleStderr');

	// 获取原始控制台输出配置
	var originalConsoleOutput = null;
	if (originalConfig && originalConfig.logging && originalConfig.logging.outputs) {
		for (var i = 0; i < originalConfig.logging.outputs.length; i++) {
			if (originalConfig.logging.outputs[i].type === 'console') {
				originalConsoleOutput = originalConfig.logging.outputs[i];
				break;
			}
		}
	}

	var consoleOutput = {
		type: 'console',
		stderr: consoleStderr !== null ? consoleStderr.checked : (originalConsoleOutput ? originalConsoleOutput.stderr : false)
	};
	config.logging.outputs.push(consoleOutput);
	
	// 调试：输出即将发送的配置数据
	console.log('即将发送的配置数据:', JSON.stringify(config, null, 2));

	// 发送配置数据
	ajxs({
		type: 'POST',
		url: '${cfgroot}/ajax_config_pure',
		data: JSON.stringify(config),
		error: function(status) {
			console.error('AJAX 请求失败，状态码:', status);
			alert('保存配置失败: HTTP ' + status + '\\n\\n请检查网络连接和服务器状态。');
		},
		success: function(data) {
			console.log('服务器响应原始数据:', data);
			console.log('响应数据长度:', data ? data.length : 0);

			try {
				// 清理响应数据
				data = data.replace(/^\s+|\s+$/g, '');

				if (!data || data.length === 0) {
					console.error('服务器返回空响应');
					alert('保存配置失败：服务器返回空响应\\n\\n这可能是服务器端处理错误，请检查服务器日志。');
					return;
				}

				console.log('清理后的响应数据:', data);

				var result = eval('(' + data + ')');
				console.log('解析后的结果:', result);

				if (result.error) {
					console.error('服务器返回错误:', result.error);
					alert('保存配置失败: ' + result.error + (result.debug ? '\\n\\n调试模式已启用，请查看控制台获取详细信息。' : ''));
				} else {
					console.log('配置保存成功');
					alert('配置保存成功！\\n\\n配置已成功保存到服务器，所有修改已生效。');
				}
			} catch(e) {
				console.error('解析响应数据失败:', e);
				console.error('响应内容:', data);
				alert('解析响应数据失败: ' + e.message + '\\n\\n响应内容: ' + data.substring(0, 200) + '\\n\\n请查看浏览器控制台获取详细信息。');
			}
		}
	});
	
	return false;
}

function resetConfig() {
	if (confirm('确定要重置所有配置吗？')) {
		// 重置为默认配置
		var defaultConfig = {
			client: {
				'customer-id': 12345,
				'client-id': 223
			},
			comms: {
				addrs: ['comm.com:12345', '127.0.0.1:50051'],
				tls: {
					enabled: false,
					'cert-dir': './certs',
					'cert-file': 'server.crt',
					'key-file': 'server.key',
					'skip-verify': true,
					'auto-generate': true
				}
			},
			logging: {
				level: 'DEBUG',
				format: 'json',
				outputs: [{
					type: 'file',
					file: '/var/log/agent.log',
					maxSize: 128,
					maxAge: 30,
					maxBackups: 10,
					compress: true
				}, {
					type: 'console',
					stderr: false
				}]
			}
		};
		populateForm(defaultConfig);
		alert('配置已重置为默认值');
	}
}

function ajxs(settings) {
	var http;
	if (typeof XMLHttpRequest != 'undefined')
		http = new XMLHttpRequest();
	else {
		try {
			http = new ActiveXObject(\"Msxml2.XMLHTTP\");
		}catch (e) {
			try {
				http = new ActiveXObject(\"Microsoft.XMLHTTP\");
			} catch (e) {return ;}
		}
	}
	http.open(settings.type, settings.url, true);
	http.setRequestHeader(\"Accept\", \"text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8\");
	http.setRequestHeader(\"Cache-Control\", \"no-cache\");
	http.setRequestHeader(\"Accept-Language\", \"zh-CN,zh;q=0.8\");
	if (settings.type === 'POST' && settings.data) {
		http.setRequestHeader(\"Content-Type\", \"application/json; charset=UTF-8\");
	}
	http.onreadystatechange = function() {
		if (http.readyState == 4) {
			if (http.status == 200)
				settings.success(http.responseText);
			else
				settings.error(http.status);
		}
	}
	http.send(settings.data || null);
}

function updateStatus() {
	if (window.location.search.indexOf('tab=status') !== -1) {
		ajxs({
			type: 'GET',
			url: '${cfgroot}/ajax_status',
			error: function(status) {
				document.getElementById('configStatus').innerHTML = '检查失败';
			},
			success: function(data) {
				try {
					var status = eval('(' + data + ')');

					// 更新配置文件状态
					var configStatusText = '';
					switch(status.config_status) {
						case 'valid': configStatusText = '<span style=\"color:green\">正常</span>'; break;
						case 'invalid': configStatusText = '<span style=\"color:red\">格式错误</span>'; break;
						case 'missing': configStatusText = '<span style=\"color:red\">文件不存在</span>'; break;
						case 'unreadable': configStatusText = '<span style=\"color:red\">无法读取</span>'; break;
						default: configStatusText = '未知';
					}
					document.getElementById('configStatus').innerHTML = configStatusText;

					// 更新最后修改时间
					document.getElementById('lastModified').innerHTML = status.last_modified || 'N/A';

					// 更新服务器连接状态
					var serverStatusHtml = '';
					if (status.server_connectivity && status.server_connectivity.length > 0) {
						for (var i = 0; i < status.server_connectivity.length; i++) {
							var server = status.server_connectivity[i];
							var statusColor = server.status === 'connected' ? 'green' : 'red';
							var statusText = server.status === 'connected' ? '已连接' : '未连接';
							serverStatusHtml += server.addr + ': <span style=\"color:' + statusColor + '\">' + statusText + '</span><br>';
						}
					} else {
						serverStatusHtml = '无服务器配置';
					}
					document.getElementById('serverStatus').innerHTML = serverStatusHtml;

					// 更新其他状态信息
					if (document.getElementById('logFileSize')) {
						document.getElementById('logFileSize').innerHTML = status.log_file_size || 'N/A';
					}
					if (document.getElementById('backupCount')) {
						document.getElementById('backupCount').innerHTML = status.backup_count || '0';
					}

				} catch(e) {
					document.getElementById('configStatus').innerHTML = '状态解析失败';
				}
			}
		});

		// 每5秒更新一次状态
		setTimeout(updateStatus, 5000);
	}
}

function onload() {
	loadConfig();
	updateStatus();
}
</script>
";

# 处理POST请求 - 保存配置
if [ "${REQUEST_METHOD}" = "POST" ]; then
	operator_check "${myself}"
	
	# 这里处理表单提交，但主要的配置保存通过AJAX接口处理
	afm_load_page 0 "${myself}"
	exit 0
fi

# 获取当前标签页
current_tab="${CGI_tab:-client}"

echo -n "
<body onload=\"onload()\">
"; cgi_print_mod_header "${LANG_001:=客户端配置}" 1280
echo -n "

<form id=\"configForm\" method=\"post\" onsubmit=\"return saveConfig()\" action=\"${myself}\">
<table width=1280 border=0 cellspacing=1 cellpadding=3 bgcolor=\"#ffffff\" style=\"margin-top:10px;\">
";

# 根据标签页显示不同内容
case "${current_tab}" in
	"client")
		echo -n "
<tr id=tblhdr><td colspan=3 align=center style=\"font-size:16px;font-weight:bold;\">${LANG_001:=客户端配置}</td></tr>
<tr id=row1>
	<td width=150 align=right>${LANG_005:=客户ID}:</td>
	<td width=200><input type=\"text\" id=\"customerId\" name=\"customerId\" style=\"width:150px\" /></td>
	<td align=left>客户的唯一标识符</td>
</tr>
<tr id=row2>
	<td align=right>${LANG_006:=客户端ID}:</td>
	<td><input type=\"text\" id=\"clientId\" name=\"clientId\" style=\"width:150px\" /></td>
	<td align=left>客户端的唯一标识符</td>
</tr>
		"
		;;
	"comms")
		echo -n "
<tr id=tblhdr><td colspan=3 align=center style=\"font-size:16px;font-weight:bold;\">${LANG_002:=通信配置}</td></tr>
<tr id=row1>
	<td width=150 align=right>${LANG_007:=服务器地址}:</td>
	<td width=400>
		<div id=\"serverAddrs\">
			<div class=\"addr-input\">
				<input type=\"text\" name=\"serverAddr\" placeholder=\"例如: comm.com:12345\" style=\"width:200px\">
				<input type=\"button\" value=\"${LANG_027:=删除}\" onclick=\"removeServerAddress(this)\">
			</div>
		</div>
		<input type=\"button\" value=\"${LANG_026:=添加地址}\" onclick=\"addServerAddress()\">
	</td>
	<td align=left>服务器地址格式: 主机名:端口号</td>
</tr>
<tr id=row2>
	<td colspan=3 align=center style=\"font-weight:bold;background-color:#e8f4fd;\">${LANG_008:=TLS 加密设置}</td>
</tr>
<tr id=row1>
	<td align=right>${LANG_009:=启用TLS}:</td>
	<td><input type=\"checkbox\" id=\"tlsEnabled\" name=\"tlsEnabled\" /> 启用 TLS 加密通信</td>
	<td align=left>是否启用 TLS 加密保护通信安全</td>
</tr>
<tr id=row2>
	<td align=right>${LANG_010:=证书目录}:</td>
	<td><input type=\"text\" id=\"tlsCertDir\" name=\"tlsCertDir\" style=\"width:200px\" placeholder=\"./certs\" /></td>
	<td align=left>证书文件存放目录</td>
</tr>
<tr id=row1>
	<td align=right>${LANG_011:=证书文件}:</td>
	<td><input type=\"text\" id=\"tlsCertFile\" name=\"tlsCertFile\" style=\"width:200px\" placeholder=\"server.crt\" /></td>
	<td align=left>服务器证书文件名</td>
</tr>
<tr id=row2>
	<td align=right>${LANG_012:=私钥文件}:</td>
	<td><input type=\"text\" id=\"tlsKeyFile\" name=\"tlsKeyFile\" style=\"width:200px\" placeholder=\"server.key\" /></td>
	<td align=left>服务器私钥文件名</td>
</tr>
<tr id=row1>
	<td align=right>${LANG_013:=跳过验证}:</td>
	<td><input type=\"checkbox\" id=\"tlsSkipVerify\" name=\"tlsSkipVerify\" checked /> 跳过服务器证书验证</td>
	<td align=left>适用于自签名证书，生产环境建议关闭</td>
</tr>
<tr id=row2>
	<td align=right>${LANG_014:=自动生成}:</td>
	<td><input type=\"checkbox\" id=\"tlsAutoGenerate\" name=\"tlsAutoGenerate\" checked /> 自动生成自签名证书</td>
	<td align=left>如果证书文件不存在，自动生成自签名证书</td>
</tr>
		"
		;;
	"logging")
		echo -n "
<tr id=tblhdr><td colspan=3 align=center style=\"font-size:16px;font-weight:bold;\">${LANG_003:=日志配置}</td></tr>
<tr id=row1>
	<td width=150 align=right>${LANG_015:=日志级别}:</td>
	<td width=200>
		<select id=\"logLevel\" name=\"logLevel\" style=\"width:150px\">
			<option value=\"DEBUG\">DEBUG</option>
			<option value=\"INFO\">INFO</option>
			<option value=\"WARN\">WARN</option>
			<option value=\"ERROR\">ERROR</option>
		</select>
	</td>
	<td align=left>选择日志输出级别</td>
</tr>
<tr id=row2>
	<td align=right>${LANG_016:=日志格式}:</td>
	<td>
		<select id=\"logFormat\" name=\"logFormat\" style=\"width:150px\">
			<option value=\"json\">JSON</option>
			<option value=\"text\">TEXT</option>
		</select>
	</td>
	<td align=left>选择日志输出格式</td>
</tr>
<tr id=row1>
	<td colspan=3 align=center style=\"font-weight:bold;background-color:#e8f4fd;\">文件输出配置</td>
</tr>
<tr id=row2>
	<td align=right>${LANG_017:=日志文件}:</td>
	<td><input type=\"text\" id=\"logFile\" name=\"logFile\" style=\"width:250px\" placeholder=\"/var/log/agent.log\" /></td>
	<td align=left>日志文件的完整路径</td>
</tr>
<tr id=row1>
	<td align=right>${LANG_018:=最大文件大小}:</td>
	<td><input type=\"text\" id=\"maxSize\" name=\"maxSize\" style=\"width:100px\" value=\"128\" /> MB</td>
	<td align=left>单个日志文件的最大大小</td>
</tr>
<tr id=row2>
	<td align=right>${LANG_019:=保留天数}:</td>
	<td><input type=\"text\" id=\"maxAge\" name=\"maxAge\" style=\"width:100px\" value=\"30\" /> 天</td>
	<td align=left>日志文件保留的最大天数</td>
</tr>
<tr id=row1>
	<td align=right>${LANG_020:=备份文件数}:</td>
	<td><input type=\"text\" id=\"maxBackups\" name=\"maxBackups\" style=\"width:100px\" value=\"10\" /></td>
	<td align=left>保留的备份日志文件数量</td>
</tr>
<tr id=row2>
	<td align=right>${LANG_021:=压缩旧文件}:</td>
	<td><input type=\"checkbox\" id=\"compress\" name=\"compress\" checked /> 是否压缩</td>
	<td align=left>是否压缩旧的日志文件</td>
</tr>
<tr id=row1>
	<td colspan=3 align=center style=\"font-weight:bold;background-color:#e8f4fd;\">控制台输出配置</td>
</tr>
<tr id=row2>
	<td align=right>${LANG_022:=控制台输出}:</td>
	<td><input type=\"checkbox\" id=\"consoleStderr\" name=\"consoleStderr\" /> 输出到标准错误</td>
	<td align=left>是否将日志输出到控制台的标准错误流</td>
</tr>
		"
		;;
	"status")
		echo -n "
<tr id=tblhdr><td colspan=3 align=center style=\"font-size:16px;font-weight:bold;\">${LANG_004:=系统状态}</td></tr>
<tr id=row1>
	<td width=150 align=right>配置文件状态:</td>
	<td width=300 id=\"configStatus\">检查中...</td>
	<td align=left>当前配置文件的状态</td>
</tr>
<tr id=row2>
	<td align=right>最后修改时间:</td>
	<td id=\"lastModified\">-</td>
	<td align=left>配置文件最后修改时间</td>
</tr>
<tr id=row1>
	<td align=right>服务器连接状态:</td>
	<td id=\"serverStatus\">检查中...</td>
	<td align=left>与配置的服务器的连接状态</td>
</tr>
<tr id=row2>
	<td align=right>日志文件大小:</td>
	<td id=\"logFileSize\">检查中...</td>
	<td align=left>当前日志文件的大小</td>
</tr>
<tr id=row1>
	<td align=right>配置备份数量:</td>
	<td id=\"backupCount\">检查中...</td>
	<td align=left>已保存的配置备份文件数量</td>
</tr>
<tr id=row2>
	<td align=right>系统信息:</td>
	<td>UniSASE Agent v1.0</td>
	<td align=left>当前系统版本信息</td>
</tr>
		"
		;;
esac

echo -n "
</table>

<table width=1280 border=0 cellspacing=0 cellpadding=3 style=\"margin-top:20px;\">
<tr>
	<td align=center>
		<input type=\"button\" value=\"${LANG_015:=加载配置}\" onclick=\"loadConfig()\" style=\"margin-right:10px;\">
		<input type=\"button\" value=\"${LANG_016:=保存配置}\" onclick=\"saveConfig()\" style=\"margin-right:10px;\">
		<input type=\"button\" value=\"${LANG_017:=重置}\" onclick=\"resetConfig()\">
	</td>
</tr>
</table>
</form>

</body>
</html>
";
